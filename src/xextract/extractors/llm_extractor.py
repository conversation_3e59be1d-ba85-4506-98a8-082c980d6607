"""LLM-based extractor for structured data extraction."""

import json
import logging
import os
import time
from typing import Any, Dict, Optional, Union

from ..llm import LLM
from ..outputs.extraction_result import ExtractionResult, TextLocation
from ..outputs.extraction_formatter import ExtractionFormatter
from .base import BaseExtractor

logger = logging.getLogger(__name__)


class LLMExtractor(BaseExtractor):
    """LLM-based extractor for structured data extraction."""

    def __init__(self, llm: LLM, **kwargs):
        """Initialize LLM extractor.

        Args:
            llm: LLM instance to use for extraction
            **kwargs: Additional options
        """
        super().__init__(**kwargs)
        self.llm = llm
        # 记录模型信息
        self.model_info = {
            "provider": getattr(llm, "provider", "unknown"),
            "model": getattr(llm, "model", "unknown"),
            "version": getattr(llm, "version", "unknown"),
        }

    def extract_structured_data(
        self,
        text: str,
        schema: Dict[str, Any],
        document_id: str = "",
        schema_id: str = "",
        document_metadata: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], ExtractionResult]:
        """Extract structured data from text according to a schema.

        Args:
            text: The text to extract from
            schema: The schema defining the structure to extract
            document_id: Document identifier
            schema_id: Schema identifier
            document_metadata: Additional document metadata
            **kwargs: Additional parameters

        Returns:
            Extracted structured data as a dictionary or ExtractionResult
        """
        # 记录开始时间
        start_time = time.time()

        # 获取返回格式
        return_format = kwargs.pop("return_format", "dict")
        # 获取文档
        doc_table = kwargs.pop("doc_table", None)

        # Convert Schema object to dict if needed
        if hasattr(schema, "to_dict"):
            schema_dict = schema.to_dict()
        else:
            schema_dict = schema

        # 从schema中提取关键信息
        simplified_schema = self._extract_schema_info(schema_dict)

        # 生成字段提示信息（包含详细的字段描述和提示）
        field_hints = self._generate_field_hints(schema_dict)

        # 使用通用的提示词模板
        import sys
        from pathlib import Path

        # 导入根目录配置
        sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))
        from config.config import config

        # 获取模板目录
        templates_dir = config.get("PROMPTS_TEMPLATES_DIR", "models/prompts")

        # 获取任务类型
        task_type = kwargs.get("task_type", "extraction")

        # 获取任务模板映射
        task_templates = {}
        if hasattr(config, "PROMPTS_TASK_TEMPLATES"):
            task_templates = config.PROMPTS_TASK_TEMPLATES

        # 获取模板路径
        # 首先尝试从schema中获取模板信息
        template_path = None
        if "metadata" in schema_dict and "prompt_template" in schema_dict["metadata"]:
            template_path = schema_dict["metadata"]["prompt_template"]
            logger.info(f"Using template from schema metadata: {template_path}")

        # 如果schema中没有指定模板，则从配置中获取
        if not template_path:
            # 尝试从任务模板映射中获取
            if task_type in task_templates:
                template_path = task_templates[task_type]
                logger.info(
                    f"Using template from config for task type '{task_type}': {template_path}"
                )
            else:
                # 使用默认的提取模板
                template_path = "extraction/extract.txt"
                logger.info(
                    f"Using default template for task type '{task_type}': {template_path}"
                )

        # 如果模板路径不是绝对路径，则拼接模板目录
        if not os.path.isabs(template_path):
            template_path = os.path.join(templates_dir, template_path)

        # 使用模板
        try:
            # 尝试加载模板
            prompt = self._format_prompt_template(
                template_path,
                {
                    "data_schema": json.dumps(simplified_schema, ensure_ascii=False),
                    "input_text": text,
                    "supplement_knowledge": field_hints,
                    "property_alias": self._format_property_alias(schema_dict),
                },
            )
            logger.info(f"Using prompt template: {template_path}")
        except Exception as e:
            logger.warning(
                f"Error loading prompt template {template_path}: {e}, using default prompt"
            )

        # Create system prompt for extraction
        system_prompt = "You are a data extraction assistant that extracts structured information from text and responds only with valid JSON."

        try:
            # Generate JSON
            result = self.llm.generate_json(
                prompt=prompt, system_prompt=system_prompt, **kwargs
            )

            # 检查结果是否是错误消息
            if isinstance(result, str) and result.startswith("Error:"):
                logger.error(f"LLM API error: {result}")
                return {"error": result, "status": "error"}
        except Exception as e:
            logger.error(f"Error during extraction: {e}")
            return {"error": f"Extraction failed: {e}", "status": "error"}

        # 记录结果类型和内容
        logger.debug(f"LLM extraction result type: {type(result)}")
        if isinstance(result, str):
            logger.debug(f"LLM extraction result (string): {result[:100]}...")
            # 尝试解析 JSON 字符串
            try:
                import json as json_module

                result = json_module.loads(result)
                logger.debug(f"Successfully parsed JSON string to dict")
            except Exception as e:
                logger.warning(f"Failed to parse JSON string: {e}")
        elif isinstance(result, dict):
            logger.debug(f"LLM extraction result (dict keys): {list(result.keys())}")
        else:
            logger.debug(f"LLM extraction result (other): {result}")

        # 计算提取时间
        extraction_time = time.time() - start_time

        # 如果需要返回简单字典，直接返回结果
        if return_format == "dict":
            return result

        # 否则，创建结构化的提取结果
        # 使用ExtractionFormatter格式化结果
        formatter = ExtractionFormatter()
        extraction_result = formatter.format_extraction_result(
            result=result,
            document_id=document_id,
            schema_id=schema_id,
            document_metadata=document_metadata,
            extraction_time=extraction_time,
            model_info=self.model_info,
            doc_table=doc_table,
        )

        # 增强提取结果
        formatter.enhance_extraction_result(extraction_result, text, self.model_info)

        return extraction_result

    def _extract_schema_info(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """从schema中提取关键信息。

        Args:
            schema: 提取模式

        Returns:
            提取的关键信息，包含字段的name、type和数组字段的子字段信息
        """
        # 处理字段
        field_info = {}
        extracted_result_format = {}
        extracted_result_format["value"], extracted_result_format["source_id"] = (
            "String",
            "Integer",
        )
        for field in schema.get("fields", []):
            # 处理数组类型
            if field.get("array", False) or field.get("type") == "array":
                # 处理数组元素的字段
                if "fields" in field:
                    signle_subfields = {}
                    subfields = []
                    for subfield in field.get("fields", []):
                        signle_subfields[subfield.get("name", "")] = (
                            extracted_result_format
                        )
                        subfields.append(signle_subfields)
                    field_info[field.get("name", "")] = subfields
            else:
                field_info[field.get("name", "")] = extracted_result_format
        return field_info

    def _generate_field_hints(self, schema: Dict[str, Any]) -> str:
        """生成字段提示信息。

        Args:
            schema: 提取模式

        Returns:
            字段提示信息
        """
        hints = []

        # 添加schema描述
        if "description" in schema and schema["description"]:
            hints.append(f"提取目标: {schema['description']}")
            hints.append("")

        hints.append("需要提取的字段:")

        # 处理字段
        for field in schema.get("fields", []):
            field_name = field.get("name", "")
            field_type = field.get("type", "string")
            field_desc = field.get("description", "")

            # 构建字段描述
            field_hint = f"- {field_name} ({field_type})"
            if field_desc:
                field_hint += f": {field_desc}"

            hints.append(field_hint)

            # 添加提示信息
            if "prompt_hint" in field and field["prompt_hint"]:
                hints.append(f"  提示: {field['prompt_hint']}")

            # 处理数组类型
            if field.get("array", False) or field.get("type") == "array":
                hints.append("  这是一个数组字段，包含以下子字段:")

                # 处理数组元素的字段
                if "fields" in field:
                    for subfield in field.get("fields", []):
                        subfield_name = subfield.get("name", "")
                        subfield_type = subfield.get("type", "string")
                        subfield_desc = subfield.get("description", "")

                        # 构建子字段描述
                        subfield_hint = f"  - {subfield_name} ({subfield_type})"
                        if subfield_desc:
                            subfield_hint += f": {subfield_desc}"

                        hints.append(subfield_hint)

                        # 添加提示信息
                        if "prompt_hint" in subfield and subfield["prompt_hint"]:
                            hints.append(f"    提示: {subfield['prompt_hint']}")

                    # 添加数组字段的子字段结构示例
                    example_item = {}
                    for subfield in field.get("fields", []):
                        subfield_name = subfield.get("name", "")
                        subfield_type = subfield.get("type", "string")
                        if subfield.get("required", False):
                            example_item[subfield_name] = f"<{subfield_type}> (必填)"
                        else:
                            example_item[subfield_name] = f"<{subfield_type}>"
        return "\n".join(hints)

    def _format_property_alias(self, schema: Dict[str, Any]) -> str:
        """格式化属性别名。

        Args:
            schema: 提取模式

        Returns:
            格式化后的属性别名
        """
        alias_text = ""

        # 遍历字段，查找别名
        for field in schema.get("fields", []):
            field_name = field.get("name", "")
            field_desc = field.get("description", "")

            # 如果有别名，添加到别名文本中
            if "aliases" in field:
                aliases = field["aliases"]
                if isinstance(aliases, list) and aliases:
                    alias_text += f"{field_name}({field_desc}): {', '.join(aliases)}\n"

            # 处理数组类型的字段
            if "fields" in field:
                for subfield in field.get("fields", []):
                    subfield_name = subfield.get("name", "")
                    subfield_desc = subfield.get("description", "")

                    # 如果有别名，添加到别名文本中
                    if "aliases" in subfield:
                        aliases = subfield["aliases"]
                        if isinstance(aliases, list) and aliases:
                            alias_text += f"{field_name}.{subfield_name}({subfield_desc}): {', '.join(aliases)}\n"

        return alias_text

    def _format_prompt_template(
        self, template_path: str, params: Dict[str, Any]
    ) -> str:
        """格式化提示词模板。

        Args:
            template_path: 模板文件路径
            params: 替换参数

        Returns:
            格式化后的提示词
        """
        # 加载模板
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                template = f.read()
        except Exception as e:
            raise ValueError(f"加载模板文件失败: {e}")
        # 替换模板中的占位符
        template = template.format(**params)
        return template

    def extract(
        self,
        source: str,
        schema: Dict[str, Any],
        document_id: str = "",
        schema_id: str = "",
        document_metadata: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], ExtractionResult]:
        """Extract structured data from text.

        Args:
            source: Text to extract from
            schema: Schema defining the structure to extract
            document_id: Document identifier
            schema_id: Schema identifier
            document_metadata: Additional document metadata
            **kwargs: Additional options

        Returns:
            Extracted data as a dictionary or ExtractionResult
        """
        return self.extract_structured_data(
            text=source,
            schema=schema,
            document_id=document_id,
            schema_id=schema_id,
            document_metadata=document_metadata,
            **kwargs,
        )
