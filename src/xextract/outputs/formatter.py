"""Unified output formatter for extraction results."""
import json
import datetime
import time
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from pandas import DataFrame

from .extraction_result import ExtractionResult, ExtractedField, create_extraction_result
from ..schema import Schema


class OutputFormatter:
    """Unified formatter for extraction results.

    This class provides comprehensive formatting capabilities for extraction results,
    supporting both simple dictionary outputs and structured ExtractionResult objects.
    """

    @staticmethod
    def create_structured_result(
        extracted_data: Dict[str, Any],
        document_id: str = "",
        schema_id: str = "",
        extraction_time: float = 0.0,
        document_metadata: Optional[Dict[str, Any]] = None,
        model_info: Optional[Dict[str, Any]] = None,
    ) -> ExtractionResult:
        """Create a structured ExtractionResult from raw extraction data.

        Args:
            extracted_data: Raw extraction result from LLM or other extractors
            document_id: Document identifier
            schema_id: Schema identifier
            extraction_time: Time taken for extraction
            document_metadata: Additional document metadata
            model_info: Information about the model used

        Returns:
            Structured ExtractionResult object
        """
        # Prepare metadata
        metadata = document_metadata or {}

        # Add model information if available
        if model_info:
            metadata.setdefault("extraction", {})
            metadata["extraction"]["model"] = model_info.get("model", "unknown")
            metadata["extraction"]["provider"] = model_info.get("provider", "unknown")
            metadata["extraction"]["version"] = model_info.get("version", "unknown")

        # Process extracted data to handle source_id information
        processed_data = OutputFormatter._process_extracted_data(extracted_data)

        # Create extraction result
        result = create_extraction_result(
            document_id=document_id,
            schema_id=schema_id,
            simple_result=processed_data["simple_data"],
            extraction_time=extraction_time,
            metadata=metadata,
        )

        # Add source_id information to field metadata
        for field_name, field in result.fields.items():
            if field_name in processed_data["source_ids"]:
                field.metadata["source_id"] = processed_data["source_ids"][field_name]

        return result

    @staticmethod
    def _process_extracted_data(extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process extracted data to separate values and source_ids.

        Args:
            extracted_data: Raw extraction data that may contain value/source_id structure

        Returns:
            Dictionary with 'simple_data' and 'source_ids' keys
        """
        simple_data = {}
        source_ids = {}

        for field_name, field_data in extracted_data.items():
            if isinstance(field_data, dict) and "value" in field_data:
                # Handle structured format: {"value": "...", "source_id": "..."}
                simple_data[field_name] = field_data["value"]
                if "source_id" in field_data:
                    source_ids[field_name] = field_data["source_id"]
            elif isinstance(field_data, list):
                # Handle array format - process each item
                processed_items = []
                for item in field_data:
                    if isinstance(item, dict) and "value" in item:
                        processed_items.append(item["value"])
                        # For arrays, we could store source_ids but it's more complex
                    else:
                        processed_items.append(item)
                simple_data[field_name] = processed_items
            else:
                # Handle simple format: direct value
                simple_data[field_name] = field_data

        return {
            "simple_data": simple_data,
            "source_ids": source_ids
        }

    @staticmethod
    def format_result(
        extracted_data: Dict[str, Any],
        schema: Schema,
        file_path: Optional[Union[str, Path]] = None,
        source_text: Optional[str] = None,
        page_info: Optional[Dict[str, Any]] = None,
        extraction_metadata: Optional[Dict[str, Any]] = None,
        include_schema: bool = False,
        include_source_text: bool = False,
        confidence_scores: Optional[Dict[str, float]] = None,
        extraction_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Format extraction result into standardized JSON format.

        Args:
            extracted_data: The extracted data
            schema: The schema used for extraction
            file_path: Path to the source file
            source_text: Source text used for extraction
            page_info: Information about pages (for PDF extraction)
            extraction_metadata: Additional metadata about the extraction process
            include_schema: Whether to include the schema in the output
            include_source_text: Whether to include the source text in the output
            confidence_scores: Confidence scores for extracted fields
            extraction_context: Additional context about the extraction

        Returns:
            Formatted extraction result
        """
        # Get current timestamp
        timestamp = datetime.datetime.now().isoformat()

        # Prepare file info
        file_info = None
        if file_path:
            path = Path(file_path)
            file_info = {
                "path": str(path),
                "name": path.name,
                "extension": path.suffix.lower(),
                "size": path.stat().st_size if path.exists() else None,
                "last_modified": datetime.datetime.fromtimestamp(
                    path.stat().st_mtime
                ).isoformat() if path.exists() else None
            }

        # Prepare schema info
        schema_info = {
            "name": schema.name,
            "version": schema.version,
            "description": schema.description
        }

        if include_schema:
            schema_info["definition"] = schema.to_dict()

        # Prepare result
        result = {
            "metadata": {
                "timestamp": timestamp,
                "schema": schema_info,
                "file": file_info,
                "extraction": extraction_metadata or {}
            },
            "data": extracted_data
        }

        # Add confidence scores if available
        if confidence_scores:
            result["confidence_scores"] = confidence_scores

        # Add source information if requested
        if include_source_text and source_text:
            result["source"] = {
                "text": source_text
            }

        # Add page information if available
        if page_info:
            result["pages"] = page_info

        # Add extraction context if available
        if extraction_context:
            result["context"] = extraction_context

        return result

    @staticmethod
    def enhance_structured_result(
        extraction_result: ExtractionResult,
        text: str,
        model_info: Optional[Dict[str, Any]] = None,
        doc_table: Optional[DataFrame] = None,
    ) -> None:
        """Enhance extraction result with additional metadata and context.

        Args:
            extraction_result: Extraction result to enhance
            text: Original text for context extraction
            model_info: Information about the model used
            doc_table: Document table (DataFrame) for source mapping
        """
        # Iterate through all fields to add context
        for field_name, field in extraction_result.fields.items():
            # Skip empty fields
            if field.value is None or field.value == "":
                continue

            # Set extraction method if not already set
            if not field.extraction_method:
                field.extraction_method = "llm" if model_info else "unknown"

            # Add model information to field metadata
            if model_info:
                field.metadata.setdefault("model", model_info)

            # Try to find context in text
            extraction_result.enhance_field(field_name, text)

            # Enhance with doc_table information if available
            OutputFormatter._enhance_field_with_doc_table(field, doc_table)

    @staticmethod
    def _enhance_field_with_doc_table(field: ExtractedField, doc_table: Optional[Any]) -> None:
        """Enhance field with information from doc_table based on source_id.

        Args:
            field: Field to enhance
            doc_table: Document table (DataFrame) for source mapping
        """
        if doc_table is None or not hasattr(field, 'metadata') or 'source_id' not in field.metadata:
            return

        try:
            source_id = field.metadata.get('source_id')
            if source_id is None or source_id == "0":
                return

            # Convert source_id to int for lookup
            source_id = int(source_id)

            # Find the row in doc_table with matching id
            matching_rows = doc_table[doc_table['id'] == source_id]

            if not matching_rows.empty:
                row = matching_rows.iloc[0]

                # Add chapter/section information
                field.metadata['source_info'] = {
                    'chapter_path': row.get('title_path', ''),
                    'text_type': row.get('type', ''),
                    'original_text': row.get('content', ''),
                    'page_range': row.get('page_range', ''),
                }

                # Update context with original text if not already set
                if not field.context and row.get('content'):
                    field.context = str(row.get('content', ''))[:200] + "..." if len(str(row.get('content', ''))) > 200 else str(row.get('content', ''))

        except (ValueError, KeyError, AttributeError):
            # Silently ignore errors in source_id lookup
            pass

    @staticmethod
    def to_json(
        result: Union[Dict[str, Any], ExtractionResult],
        indent: int = 2,
        ensure_ascii: bool = False,
        **kwargs
    ) -> str:
        """Convert result to JSON string.

        Args:
            result: Extraction result (dict or ExtractionResult)
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output
            **kwargs: Additional arguments for ExtractionResult.to_dict()

        Returns:
            JSON string
        """
        if isinstance(result, ExtractionResult):
            data = result.to_dict(**kwargs)
        else:
            data = result
        return json.dumps(data, indent=indent, ensure_ascii=ensure_ascii)

    @staticmethod
    def format_for_api(
        extraction_result: ExtractionResult,
        include_metadata: bool = True,
        include_confidence: bool = True,
    ) -> Dict[str, Any]:
        """Format extraction result for API response.

        Args:
            extraction_result: Extraction result to format
            include_metadata: Whether to include metadata
            include_confidence: Whether to include confidence scores

        Returns:
            API-formatted result
        """
        result = {
            "status": "success",
            "data": extraction_result.get_simple_result(),
            "document_id": extraction_result.document_id,
            "schema_id": extraction_result.schema_id,
        }

        if include_metadata:
            result["metadata"] = extraction_result.metadata

        # Add confidence scores if available
        if include_confidence:
            confidence_scores = {}
            for field_name, field in extraction_result.fields.items():
                if "confidence" in field.metadata:
                    confidence_scores[field_name] = field.metadata["confidence"]
            if confidence_scores:
                result["confidence_scores"] = confidence_scores

        return result


# Backward compatibility aliases
ExtractionResultFormatter = OutputFormatter
ExtractionFormatter = OutputFormatter