"""Unified output formatter for extraction results."""
import json
import time
from typing import Any, Dict, Optional, Union

from .extraction_result import ExtractionR<PERSON>ult, ExtractedField, create_extraction_result


class OutputFormatter:
    """Unified formatter for extraction results.

    This class provides comprehensive formatting capabilities for extraction results,
    supporting both simple dictionary outputs and structured ExtractionResult objects.
    """

    @staticmethod
    def create_structured_result(
        extracted_data: Dict[str, Any],
        document_id: str = "",
        schema_id: str = "",
        extraction_time: float = 0.0,
        document_metadata: Optional[Dict[str, Any]] = None,
        model_info: Optional[Dict[str, Any]] = None,
    ) -> ExtractionResult:
        """Create a structured ExtractionResult from raw extraction data.

        Args:
            extracted_data: Raw extraction result from LLM or other extractors
            document_id: Document identifier
            schema_id: Schema identifier
            extraction_time: Time taken for extraction
            document_metadata: Additional document metadata
            model_info: Information about the model used

        Returns:
            Structured ExtractionResult object
        """
        # Prepare metadata
        metadata = document_metadata or {}

        # Add model information if available
        if model_info:
            metadata.setdefault("extraction", {})
            metadata["extraction"]["model"] = model_info.get("model", "unknown")
            metadata["extraction"]["provider"] = model_info.get("provider", "unknown")
            metadata["extraction"]["version"] = model_info.get("version", "unknown")

        # Process extracted data to handle source_id information
        processed_data = OutputFormatter._process_extracted_data(extracted_data)

        # Create extraction result
        result = create_extraction_result(
            document_id=document_id,
            schema_id=schema_id,
            simple_result=processed_data["simple_data"],
            extraction_time=extraction_time,
            metadata=metadata,
        )

        # Add source_id information to field metadata
        for field_name, field in result.fields.items():
            if field_name in processed_data["source_ids"]:
                field.metadata["source_id"] = processed_data["source_ids"][field_name]

        return result

    @staticmethod
    def _process_extracted_data(extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process extracted data to separate values and source_ids."""
        simple_data = {}
        source_ids = {}

        for field_name, field_data in extracted_data.items():
            if isinstance(field_data, dict) and "value" in field_data:
                # Handle structured format: {"value": "...", "source_id": "..."}
                simple_data[field_name] = field_data["value"]
                if "source_id" in field_data:
                    source_ids[field_name] = field_data["source_id"]
            else:
                # Handle simple format: direct value
                simple_data[field_name] = field_data

        return {"simple_data": simple_data, "source_ids": source_ids}



    @staticmethod
    def enhance_structured_result(
        extraction_result: ExtractionResult,
        text: str,
        model_info: Optional[Dict[str, Any]] = None,
        doc_table: Optional[Any] = None,
    ) -> None:
        """Enhance extraction result with additional metadata and context.

        Args:
            extraction_result: Extraction result to enhance
            text: Original text for context extraction
            model_info: Information about the model used
            doc_table: Document table (DataFrame) for source mapping
        """
        # Iterate through all fields to add context
        for field_name, field in extraction_result.fields.items():
            # Skip empty fields
            if field.value is None or field.value == "":
                continue

            # Set extraction method if not already set
            if not field.extraction_method:
                field.extraction_method = "llm" if model_info else "unknown"

            # Add model information to field metadata
            if model_info:
                field.metadata.setdefault("model", model_info)

            # Try to find context in text
            extraction_result.enhance_field(field_name, text)

            # Enhance with doc_table information if available
            OutputFormatter._enhance_field_with_doc_table(field, doc_table)

    @staticmethod
    def _enhance_field_with_doc_table(field: ExtractedField, doc_table: Optional[Any]) -> None:
        """Enhance field with information from doc_table based on source_id."""
        if (doc_table is None or
            not hasattr(field, 'metadata') or
            'source_id' not in field.metadata):
            return

        try:
            source_id = field.metadata.get('source_id')
            if not source_id or source_id == "0":
                return

            # Find matching row
            matching_rows = doc_table[doc_table['id'] == int(source_id)]
            if matching_rows.empty:
                return

            row = matching_rows.iloc[0]

            # Add source information
            field.metadata['source_info'] = {
                'chapter_path': row.get('title_path', ''),
                'text_type': row.get('type', ''),
                'original_text': row.get('content', ''),
                'page_range': row.get('page_range', ''),
            }

            # Update context if not set
            if not field.context and row.get('content'):
                content = str(row.get('content', ''))
                field.context = content[:200] + "..." if len(content) > 200 else content

        except (ValueError, KeyError, AttributeError):
            pass

    @staticmethod
    def to_json(
        result: Union[Dict[str, Any], ExtractionResult],
        indent: int = 2,
        ensure_ascii: bool = False,
        **kwargs
    ) -> str:
        """Convert result to JSON string.

        Args:
            result: Extraction result (dict or ExtractionResult)
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output
            **kwargs: Additional arguments for ExtractionResult.to_dict()

        Returns:
            JSON string
        """
        if isinstance(result, ExtractionResult):
            data = result.to_dict(**kwargs)
        else:
            data = result
        return json.dumps(data, indent=indent, ensure_ascii=ensure_ascii)

    @staticmethod
    def format_for_api(
        extraction_result: ExtractionResult,
        include_metadata: bool = True,
        include_confidence: bool = True,
    ) -> Dict[str, Any]:
        """Format extraction result for API response."""
        result = {
            "status": "success",
            "data": extraction_result.get_simple_result(),
            "document_id": extraction_result.document_id,
            "schema_id": extraction_result.schema_id,
        }

        if include_metadata:
            result["metadata"] = extraction_result.metadata

        if include_confidence:
            confidence_scores = {
                field_name: field.metadata["confidence"]
                for field_name, field in extraction_result.fields.items()
                if "confidence" in field.metadata
            }
            if confidence_scores:
                result["confidence_scores"] = confidence_scores

        return result


# Backward compatibility aliases
ExtractionResultFormatter = OutputFormatter
ExtractionFormatter = OutputFormatter