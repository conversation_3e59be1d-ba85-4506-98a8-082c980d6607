"""Output formatter for extraction results."""
import json
import datetime
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from ..schema import Schema


class ExtractionResultFormatter:
    """Formatter for extraction results.
    
    This class formats extraction results into a standardized JSON format
    that includes metadata, source information, and extraction details.
    """
    
    @staticmethod
    def format_result(
        extracted_data: Dict[str, Any],
        schema: Schema,
        file_path: Optional[Union[str, Path]] = None,
        source_text: Optional[str] = None,
        page_info: Optional[Dict[str, Any]] = None,
        extraction_metadata: Optional[Dict[str, Any]] = None,
        include_schema: bool = False,
        include_source_text: bool = False,
        confidence_scores: Optional[Dict[str, float]] = None,
        extraction_context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Format extraction result into standardized JSON format.
        
        Args:
            extracted_data: The extracted data
            schema: The schema used for extraction
            file_path: Path to the source file
            source_text: Source text used for extraction
            page_info: Information about pages (for PDF extraction)
            extraction_metadata: Additional metadata about the extraction process
            include_schema: Whether to include the schema in the output
            include_source_text: Whether to include the source text in the output
            confidence_scores: Confidence scores for extracted fields
            extraction_context: Additional context about the extraction
            
        Returns:
            Formatted extraction result
        """
        # Get current timestamp
        timestamp = datetime.datetime.now().isoformat()
        
        # Prepare file info
        file_info = None
        if file_path:
            path = Path(file_path)
            file_info = {
                "path": str(path),
                "name": path.name,
                "extension": path.suffix.lower(),
                "size": path.stat().st_size if path.exists() else None,
                "last_modified": datetime.datetime.fromtimestamp(
                    path.stat().st_mtime
                ).isoformat() if path.exists() else None
            }
        
        # Prepare schema info
        schema_info = {
            "name": schema.name,
            "version": schema.version,
            "description": schema.description
        }
        
        if include_schema:
            schema_info["definition"] = schema.to_dict()
        
        # Prepare result
        result = {
            "metadata": {
                "timestamp": timestamp,
                "schema": schema_info,
                "file": file_info,
                "extraction": extraction_metadata or {}
            },
            "data": extracted_data
        }
        
        # Add confidence scores if available
        if confidence_scores:
            result["confidence_scores"] = confidence_scores
        
        # Add source information if requested
        if include_source_text and source_text:
            result["source"] = {
                "text": source_text
            }
        
        # Add page information if available
        if page_info:
            result["pages"] = page_info
        
        # Add extraction context if available
        if extraction_context:
            result["context"] = extraction_context
        
        return result
    
    @staticmethod
    def to_json(
        result: Dict[str, Any],
        indent: int = 2,
        ensure_ascii: bool = False
    ) -> str:
        """Convert result to JSON string.
        
        Args:
            result: Extraction result
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output
            
        Returns:
            JSON string
        """
        return json.dumps(result, indent=indent, ensure_ascii=ensure_ascii)
