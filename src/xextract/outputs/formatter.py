"""Unified output formatter for extraction results."""
import json
import time
from typing import Any, Dict, Optional, Union

from .extraction_result import ExtractionResult, ExtractedField, create_extraction_result


class OutputFormatter:
    """输出格式化器，支持两种格式化目标：

    1. 原始结果输出：大模型的直接提取结果
    2. 增强结果输出：添加了溯源信息的结果
    """

    @staticmethod
    def create_raw_result(
        extracted_data: Dict[str, Any],
        document_id: str = "",
        schema_id: str = "",
        extraction_time: float = 0.0,
        document_metadata: Optional[Dict[str, Any]] = None,
        model_info: Optional[Dict[str, Any]] = None,
    ) -> ExtractionResult:
        """创建原始提取结果（不添加溯源信息）。

        Args:
            extracted_data: 大模型的原始提取结果
            document_id: 文档标识符
            schema_id: 模式标识符
            extraction_time: 提取耗时
            document_metadata: 文档元数据
            model_info: 模型信息

        Returns:
            原始提取结果对象
        """
        # 准备基础元数据
        metadata = document_metadata or {}
        if model_info:
            metadata.setdefault("extraction", {})["model"] = model_info

        # 处理提取数据，分离值和source_id
        processed_data = OutputFormatter._process_extracted_data(extracted_data)

        # 创建提取结果
        result = create_extraction_result(
            document_id=document_id,
            schema_id=schema_id,
            simple_result=processed_data["simple_data"],
            extraction_time=extraction_time,
            metadata=metadata,
        )

        # 保存source_id信息到字段元数据（用于后续溯源）
        for field_name, field in result.fields.items():
            if field_name in processed_data["source_ids"]:
                field.metadata["source_id"] = processed_data["source_ids"][field_name]

        return result

    @staticmethod
    def _process_extracted_data(extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process extracted data to separate values and source_ids."""
        simple_data = {}
        source_ids = {}

        for field_name, field_data in extracted_data.items():
            if isinstance(field_data, dict) and "value" in field_data:
                # Handle structured format: {"value": "...", "source_id": "..."}
                simple_data[field_name] = field_data["value"]
                if "source_id" in field_data:
                    source_ids[field_name] = field_data["source_id"]
            else:
                # Handle simple format: direct value
                simple_data[field_name] = field_data

        return {"simple_data": simple_data, "source_ids": source_ids}

    @staticmethod
    def create_enhanced_result(
        extracted_data: Dict[str, Any],
        doc_table: Any,
        document_id: str = "",
        schema_id: str = "",
        extraction_time: float = 0.0,
        document_metadata: Optional[Dict[str, Any]] = None,
        model_info: Optional[Dict[str, Any]] = None,
    ) -> ExtractionResult:
        """创建增强提取结果（包含溯源信息）。

        Args:
            extracted_data: 大模型的原始提取结果
            doc_table: 文档表格，用于溯源查找
            document_id: 文档标识符
            schema_id: 模式标识符
            extraction_time: 提取耗时
            document_metadata: 文档元数据
            model_info: 模型信息

        Returns:
            增强的提取结果对象
        """
        # 先创建原始结果
        result = OutputFormatter.create_raw_result(
            extracted_data=extracted_data,
            document_id=document_id,
            schema_id=schema_id,
            extraction_time=extraction_time,
            document_metadata=document_metadata,
            model_info=model_info,
        )

        # 添加溯源信息
        OutputFormatter.add_source_tracing(result, doc_table)

        return result

    @staticmethod
    def add_source_tracing(
        extraction_result: ExtractionResult,
        doc_table: Optional[Any] = None,
    ) -> None:
        """为提取结果添加溯源信息。

        Args:
            extraction_result: 要增强的提取结果
            doc_table: 文档表格，用于溯源查找
        """
        if doc_table is None:
            return

        # 为每个字段添加溯源信息
        for field_name, field in extraction_result.fields.items():
            if field.value is None or field.value == "":
                continue

            # 添加溯源信息
            OutputFormatter._add_field_source_info(field, doc_table)

    @staticmethod
    def _add_field_source_info(field: ExtractedField, doc_table: Optional[Any]) -> None:
        """根据source_id从doc_table中添加溯源信息到字段。"""
        if (doc_table is None or
            not hasattr(field, 'metadata') or
            'source_id' not in field.metadata):
            return

        try:
            source_id = field.metadata.get('source_id')
            if not source_id or source_id == "0":
                return

            # Find matching row
            matching_rows = doc_table[doc_table['id'] == int(source_id)]
            if matching_rows.empty:
                return

            row = matching_rows.iloc[0]

            # Add source information
            field.metadata['source_info'] = {
                'chapter_path': row.get('title_path', ''),
                'text_type': row.get('type', ''),
                'original_text': row.get('content', ''),
                'page_range': row.get('page_range', ''),
            }

            # Update context if not set
            if not field.context and row.get('content'):
                content = str(row.get('content', ''))
                field.context = content[:200] + "..." if len(content) > 200 else content

        except (ValueError, KeyError, AttributeError):
            pass

    @staticmethod
    def to_json(
        result: Union[Dict[str, Any], ExtractionResult],
        indent: int = 2,
        ensure_ascii: bool = False,
        **kwargs
    ) -> str:
        """Convert result to JSON string.

        Args:
            result: Extraction result (dict or ExtractionResult)
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output
            **kwargs: Additional arguments for ExtractionResult.to_dict()

        Returns:
            JSON string
        """
        if isinstance(result, ExtractionResult):
            data = result.to_dict(**kwargs)
        else:
            data = result
        return json.dumps(data, indent=indent, ensure_ascii=ensure_ascii)

    @staticmethod
    def format_for_api(
        extraction_result: ExtractionResult,
        include_metadata: bool = True,
        include_confidence: bool = True,
    ) -> Dict[str, Any]:
        """Format extraction result for API response."""
        result = {
            "status": "success",
            "data": extraction_result.get_simple_result(),
            "document_id": extraction_result.document_id,
            "schema_id": extraction_result.schema_id,
        }

        if include_metadata:
            result["metadata"] = extraction_result.metadata

        if include_confidence:
            confidence_scores = {
                field_name: field.metadata["confidence"]
                for field_name, field in extraction_result.fields.items()
                if "confidence" in field.metadata
            }
            if confidence_scores:
                result["confidence_scores"] = confidence_scores

        return result


# Backward compatibility aliases and methods
ExtractionResultFormatter = OutputFormatter
ExtractionFormatter = OutputFormatter

# 向后兼容的方法
OutputFormatter.create_structured_result = OutputFormatter.create_raw_result
OutputFormatter.enhance_structured_result = OutputFormatter.add_source_tracing