"""Extraction result formatter for LLM extraction."""

import json
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pandas import DataFrame

from ..outputs.extraction_result import ExtractedField as Field
from ..outputs.extraction_result import (
    ExtractionResult,
    TextLocation,
    create_extraction_result,
)
from ..schema import Schema

logger = logging.getLogger(__name__)


class ExtractionFormatter:
    """Formatter for LLM extraction results.

    This class handles the formatting of extraction results from LLM extractors,
    including enhancing results with metadata, context, and confidence scores.
    """

    @staticmethod
    def format_extraction_result(
        result: Dict[str, Any],
        document_id: str = "",
        schema_id: str = "",
        document_metadata: Optional[Dict[str, Any]] = None,
        extraction_time: float = 0.0,
        model_info: Optional[Dict[str, Any]] = None,
        doc_table: Optional[DataFrame] = None,
    ) -> ExtractionResult:
        """Format extraction result into ExtractionResult object.

        Args:
            result: Raw extraction result from LLM
            document_id: Document identifier
            schema_id: Schema identifier
            document_metadata: Additional document metadata
            extraction_time: Time taken for extraction
            model_info: Information about the LLM model used

        Returns:
            Formatted ExtractionResult object
        """
        # Use default values if not provided
        if not document_id:
            document_id = "unknown"
        if not schema_id:
            schema_id = "unknown"

        # Prepare metadata
        metadata = document_metadata or {}

        # Add extraction information to metadata
        metadata["extraction"] = {
            "timestamp": time.time(),
            "duration": extraction_time,
        }

        # Add model information if available
        if model_info:
            metadata["extraction"]["model"] = model_info.get("model", "unknown")
            metadata["extraction"]["provider"] = model_info.get("provider", "unknown")
            metadata["extraction"]["version"] = model_info.get("version", "unknown")

        # Create extraction result
        extraction_result = create_extraction_result(
            document_id=document_id,
            schema_id=schema_id,
            simple_result=result,
            extraction_time=extraction_time,
            metadata=metadata,
        )

        return extraction_result

    @staticmethod
    def enhance_extraction_result(
        extraction_result: ExtractionResult,
        text: str,
        model_info: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Enhance extraction result with additional metadata.

        Args:
            extraction_result: Extraction result to enhance
            text: Original text
            model_info: Information about the LLM model used
        """
        # Iterate through all fields
        for field_name, field in extraction_result.fields.items():
            # Skip empty fields
            if field.value is None or field.value == "":
                continue

            # Try to find field value in text
            if isinstance(field.value, str):
                try:
                    # Simple string matching
                    value_str = str(field.value)
                    if value_str in text:
                        # Found the field value, extract context
                        pos = text.find(value_str)
                        start = max(0, pos - 50)
                        end = min(len(text), pos + len(value_str) + 50)
                        context = text[start:end]

                        # Set context
                        field.context = context

                        # Create location information
                        location = TextLocation(
                            page=0,  # Default page number
                            text_type="text",  # Default type
                        )
                        field.locations.append(location)
                except Exception as e:
                    logger.warning(f"Error enhancing field {field_name}: {e}")

            # Set extraction method
            field.extraction_method = "llm"

            # Add model information to metadata
            if model_info:
                field.metadata["model"] = model_info

    @staticmethod
    def to_json(
        extraction_result: ExtractionResult, indent: int = 2, ensure_ascii: bool = False
    ) -> str:
        """Convert extraction result to JSON string.

        Args:
            extraction_result: Extraction result
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output

        Returns:
            JSON string
        """
        return json.dumps(
            extraction_result.to_dict(), indent=indent, ensure_ascii=ensure_ascii
        )
