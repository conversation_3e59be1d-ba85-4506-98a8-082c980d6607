"""Data models for extraction results."""
from typing import Any, Dict, List, Optional, Union


class TextLocation:
    """Text location in a document.
    
    Attributes:
        page: Page number
        text_type: Type of text (text, title, table, etc.)
    """
    def __init__(self, page=0, text_type="text"):
        self.page = page
        self.text_type = text_type
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            "page": self.page,
            "text_type": self.text_type
        }


class ExtractedField:
    """Extracted field with metadata.
    
    Attributes:
        value: Field value
        context: Context text
        locations: List of text locations
        extraction_method: Method used for extraction
        metadata: Additional metadata
    """
    def __init__(self, value=None):
        self.value = value
        self.context = ""
        self.locations = []
        self.extraction_method = ""
        self.metadata = {}
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            "value": self.value,
            "context": self.context,
            "locations": [loc.to_dict() for loc in self.locations],
            "extraction_method": self.extraction_method,
            "metadata": self.metadata
        }


class ExtractionResult:
    """Extraction result with metadata.
    
    Attributes:
        document_id: Document identifier
        schema_id: Schema identifier
        fields: Dictionary of extracted fields
        metadata: Additional metadata
    """
    def __init__(self, document_id="", schema_id=""):
        self.document_id = document_id
        self.schema_id = schema_id
        self.fields = {}
        self.metadata = {}
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.
        
        Returns:
            Dictionary representation
        """
        fields_dict = {}
        for field_name, field in self.fields.items():
            fields_dict[field_name] = field.to_dict()
            
        return {
            "document_id": self.document_id,
            "schema_id": self.schema_id,
            "fields": fields_dict,
            "metadata": self.metadata
        }


def create_extraction_result(
    document_id="", schema_id="", simple_result=None, extraction_time=0, metadata=None
) -> ExtractionResult:
    """Create extraction result from simple result.
    
    Args:
        document_id: Document identifier
        schema_id: Schema identifier
        simple_result: Simple result dictionary
        extraction_time: Time taken for extraction
        metadata: Additional metadata
        
    Returns:
        Extraction result
    """
    result = ExtractionResult(document_id=document_id, schema_id=schema_id)
    result.metadata = metadata or {}

    # 添加简单结果到字段
    if simple_result and isinstance(simple_result, dict):
        for field_name, field_value in simple_result.items():
            field = ExtractedField(value=field_value)
            result.fields[field_name] = field

    return result
