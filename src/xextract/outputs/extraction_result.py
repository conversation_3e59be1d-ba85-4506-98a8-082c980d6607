"""Data models for extraction results."""
import json
import time
from typing import Any, Dict, List, Optional, Union


class TextLocation:
    """Text location in a document.

    Attributes:
        page: Page number
        text_type: Type of text (text, title, table, etc.)
    """
    def __init__(self, page=0, text_type="text"):
        self.page = page
        self.text_type = text_type

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.

        Returns:
            Dictionary representation
        """
        return {
            "page": self.page,
            "text_type": self.text_type
        }


class ExtractedField:
    """Extracted field with metadata.

    Attributes:
        value: Field value
        context: Context text
        locations: List of text locations
        extraction_method: Method used for extraction
        metadata: Additional metadata
    """
    def __init__(self, value=None):
        self.value = value
        self.context = ""
        self.locations = []
        self.extraction_method = ""
        self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.

        Returns:
            Dictionary representation
        """
        return {
            "value": self.value,
            "context": self.context,
            "locations": [loc.to_dict() for loc in self.locations],
            "extraction_method": self.extraction_method,
            "metadata": self.metadata
        }


class ExtractionResult:
    """Extraction result with metadata.

    Attributes:
        document_id: Document identifier
        schema_id: Schema identifier
        fields: Dictionary of extracted fields
        metadata: Additional metadata
    """
    def __init__(self, document_id="", schema_id=""):
        self.document_id = document_id
        self.schema_id = schema_id
        self.fields = {}
        self.metadata = {}

    def to_dict(self, include_metadata: bool = True, simple_format: bool = False) -> Dict[str, Any]:
        """Convert to dictionary.

        Args:
            include_metadata: Whether to include metadata
            simple_format: Whether to use simple format (values only)

        Returns:
            Dictionary representation
        """
        if simple_format:
            # Simple format: only field values
            return {field_name: field.value for field_name, field in self.fields.items()}

        # Full format with field details
        fields_dict = {}
        for field_name, field in self.fields.items():
            fields_dict[field_name] = field.to_dict()

        result = {
            "document_id": self.document_id,
            "schema_id": self.schema_id,
            "fields": fields_dict,
        }

        if include_metadata:
            result["metadata"] = self.metadata

        return result

    def to_json(self, indent: int = 2, ensure_ascii: bool = False, **kwargs) -> str:
        """Convert to JSON string.

        Args:
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output
            **kwargs: Additional arguments for to_dict()

        Returns:
            JSON string
        """
        return json.dumps(self.to_dict(**kwargs), indent=indent, ensure_ascii=ensure_ascii)

    def get_simple_result(self) -> Dict[str, Any]:
        """Get simple result with only field values.

        Returns:
            Dictionary with field names and values
        """
        return {field_name: field.value for field_name, field in self.fields.items()}

    def add_field(self, name: str, value: Any, context: str = "",
                  extraction_method: str = "", metadata: Optional[Dict[str, Any]] = None) -> None:
        """Add a field to the extraction result.

        Args:
            name: Field name
            value: Field value
            context: Context text
            extraction_method: Method used for extraction
            metadata: Additional metadata
        """
        field = ExtractedField(value=value)
        field.context = context
        field.extraction_method = extraction_method
        field.metadata = metadata or {}
        self.fields[name] = field

    def enhance_field(self, field_name: str, text: str, page: int = 0,
                     text_type: str = "text") -> None:
        """Enhance a field with context and location information.

        Args:
            field_name: Name of the field to enhance
            text: Source text to search for context
            page: Page number
            text_type: Type of text (text, title, table, etc.)
        """
        if field_name not in self.fields:
            return

        field = self.fields[field_name]
        if field.value is None or field.value == "":
            return

        # Try to find field value in text for context
        if isinstance(field.value, str):
            value_str = str(field.value)
            if value_str in text:
                # Found the field value, extract context
                pos = text.find(value_str)
                start = max(0, pos - 50)
                end = min(len(text), pos + len(value_str) + 50)
                field.context = text[start:end]

                # Add location information
                location = TextLocation(page=page, text_type=text_type)
                field.locations.append(location)


def create_extraction_result(
    document_id: str = "",
    schema_id: str = "",
    simple_result: Optional[Dict[str, Any]] = None,
    extraction_time: float = 0.0,
    metadata: Optional[Dict[str, Any]] = None
) -> ExtractionResult:
    """Create extraction result from simple result.

    Args:
        document_id: Document identifier
        schema_id: Schema identifier
        simple_result: Simple result dictionary
        extraction_time: Time taken for extraction
        metadata: Additional metadata

    Returns:
        Extraction result
    """
    result = ExtractionResult(document_id=document_id, schema_id=schema_id)
    result.metadata = metadata or {}

    # Add extraction timing information
    result.metadata.setdefault("extraction", {})
    result.metadata["extraction"]["timestamp"] = time.time()
    if extraction_time > 0:
        result.metadata["extraction"]["duration"] = extraction_time

    # Add simple result to fields
    if simple_result and isinstance(simple_result, dict):
        for field_name, field_value in simple_result.items():
            field = ExtractedField(value=field_value)
            result.fields[field_name] = field

    return result
