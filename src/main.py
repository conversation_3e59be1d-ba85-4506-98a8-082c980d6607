#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : main.py
<AUTHOR> XExtract Team
@Date    : 2023-05-08
@Desc    : Main entry point for XExtract

支持四种模式:
1. server: 启动API服务器
2. extract: 从PDF文件中提取结构化数据
3. document: 解析文档结构
4. compare: 回归测试，比对提取结果
"""

import asyncio
import sys
import logging
import os
import json
import time
import uuid
import click
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(project_root))

# 导入配置
from config.config import config, ROOT_DIR

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("xextract.main")


def get_version() -> str:
    """获取XExtract版本号。

    Returns:
        版本号字符串
    """
    return config.VERSION


@click.group(help=f"XExtract - Document Extraction Engine v{get_version()}")
@click.version_option(version=get_version())
@click.option("--debug", is_flag=True, help="Enable debug mode")
@click.pass_context
def cli(ctx, debug):
    """XExtract 命令行工具。

    支持四种模式:
    1. server: 启动API服务器
    2. extract: 从PDF文件中提取结构化数据
    3. document: 解析文档结构
    4. compare: 回归测试，比对提取结果
    """
    # 保存全局选项到上下文
    ctx.ensure_object(dict)
    ctx.obj["DEBUG"] = debug

    # 设置日志级别
    if debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug mode enabled")


@cli.command(help="列出可用的LLM模型")
@click.option("--json", is_flag=True, help="Output in JSON format")
@click.pass_context
def list_models(ctx, json):
    """列出配置中可用的LLM模型。"""
    # 获取模型列表
    models = config.llm_models

    if json:
        # 输出JSON格式
        print(json.dumps(models, indent=2, ensure_ascii=False))
    else:
        # 输出表格格式
        print("\n可用的LLM模型:")
        print("-" * 80)
        print(f"{'ID':<15} {'名称':<20} {'提供商':<10} {'默认':<6} {'描述'}")
        print("-" * 80)

        for model in models:
            is_default = "✓" if model.get("is_default", False) else ""
            print(
                f"{model['id']:<15} {model['name']:<20} {model['provider']:<10} {is_default:<6} {model.get('description', '')}"
            )

        print("-" * 80)
        print(f"共 {len(models)} 个模型")

    return 0


@cli.command(help="启动API服务器")
@click.option("--host", default="0.0.0.0", help="Host to bind to")
@click.option("--port", type=int, default=8000, help="Port to bind to")
@click.option("--reload", is_flag=True, help="Enable auto-reload")
@click.pass_context
def server(ctx, host, port, reload):
    """启动XExtract API服务器。"""
    debug = ctx.obj.get("DEBUG", False)

    # 导入uvicorn
    import uvicorn

    # 应用模块路径
    app_module = "src.server.api.app:app"

    logger.info(f"Starting XExtract API server on {host}:{port}")

    # 运行服务器
    uvicorn.run(
        app_module,
        host=host,
        port=port,
        reload=reload,
        log_level="debug" if debug else "info",
    )
    return 0


@cli.command(help="从PDF文件中提取结构化数据")
@click.argument("pdf_path", type=click.Path(exists=True))
@click.argument("schema", type=click.Path(exists=True))
@click.option(
    "--output", "-o", default="output", help="Output file for extraction results"
)
@click.option("--recursive", is_flag=True, help="Recursively process directories")
@click.option("--model", "-m", help="LLM model ID to use (overrides default model)")
@click.option("--return-structured-result", "-rso", default=True, help="Return structured result")
@click.pass_context
def extract(ctx, pdf_path, schema, output, recursive, model, return_structured_result):
    """从PDF文件中提取结构化数据。"""
    debug = ctx.obj.get("DEBUG", False)

    # 导入提取引擎
    from src.xextract.engine import FileExtractorDriver

    logger.info(f"Extracting data from {pdf_path} using schema {schema}")

    # 创建输出目录
    output_dir = os.path.dirname(output) if output else None
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Created output directory: {output_dir}")

    # 获取LLM配置（如果指定了模型则使用指定模型，否则使用默认配置）
    llm_config = config.get_model_by_id(model) if model else config.llm

    if model:
        logger.info(f"Using specified model: {llm_config['id']}")

    logger.info(
        f"Using LLM: {llm_config['provider']}/{llm_config['id']}, "
        f"temperature={llm_config['temperature']}, timeout={llm_config['timeout']}s"
    )

    # 创建提取引擎
    engine = FileExtractorDriver(
        extractor_type="llm",
        llm_provider=llm_config["provider"],
        llm_model=llm_config["id"],
        llm_api_key=llm_config["api_key"],
        llm_api_base_url=llm_config["api_base_url"],
        llm_timeout=llm_config["timeout"],
        llm_max_retries=llm_config["max_retries"],
        llm_temperature=llm_config["temperature"],
        llm_max_tokens=llm_config["max_tokens"],
    )

    # 提取数据
    pdf_path = os.path.abspath(pdf_path)

    # 确定是处理单个文件还是目录
    if os.path.isdir(pdf_path):
        # 处理目录
        logger.info(f"Processing directory: {pdf_path}")

        # 获取目录中的所有 PDF 文件
        pdf_files = []
        if recursive:
            for root, _, files in os.walk(pdf_path):
                for file in files:
                    if file.lower().endswith(".pdf"):
                        pdf_files.append(os.path.join(root, file))
        else:
            pdf_files = [
                os.path.join(pdf_path, f)
                for f in os.listdir(pdf_path)
                if f.lower().endswith(".pdf")
            ]

        # 处理每个文件
        results = []
        successful = 0
        failed = 0

        for file in pdf_files:
            try:
                # 确定输出文件路径
                output_dir = Path(output)
                output_path = output_dir / f"{os.path.basename(file)}.json"

                # 使用FileExtractorDriver提取数据
                result = engine.extract_from_file_sync(
                    file_path=file,
                    schema=schema,
                    include_metadata=True,
                    return_structured_result=return_structured_result
                )

                # 添加文件信息到结果
                if isinstance(result, dict):
                    result["file_name"] = os.path.basename(file)

                # 保存结果
                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                    logger.info(f"Result saved to {output_path}")

                results.append(result)
                successful += 1

            except Exception as e:
                logger.error(f"Failed to process {file}: {e}")
                failed += 1
                results.append({"file": file, "error": str(e)})

        # 输出结果摘要
        logger.info(
            f"Processed {len(results)} files: {successful} successful, {failed} failed"
        )

    else:
        # 处理单个文件
        logger.info(f"Processing file: {pdf_path}")

        # 确定输出文件路径
        output_path = output

        # 使用FileExtractorDriver提取数据
        result = engine.extract_from_file_sync(
            file_path=pdf_path,
            schema=schema,
            output_path=output_path,
            include_metadata=True,
            return_structured_result=return_structured_result,
        )

        # 添加文件信息到结果
        if isinstance(result, dict):
            result["file_name"] = os.path.basename(pdf_path)
            result["timestamp"] = time.time()
            result["schema"] = os.path.basename(schema)

        # 保存结果
        if output_path:
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"Result saved to {output_path}")
        else:
            # 打印结果到控制台
            print(json.dumps(result, indent=2, ensure_ascii=False))

    return 0


@cli.command(help="解析文档结构")
@click.argument("input_file", type=click.Path(exists=True))
@click.option(
    "--output-dir", "-o", default="output", help="Output directory for parsing results"
)
@click.option(
    "--format",
    "-f",
    type=click.Choice(["json", "md", "md_with_id", "csv"]),
    default="md",
    help="Output format (json, md, csv)",
)
@click.option("--task-id", help="Task ID for tracking (generated if not provided)")
@click.pass_context
def document(ctx, input_file, output_dir, format, task_id):
    """解析文档结构。"""
    # 导入解析器
    from xextract.parsers.api_parser import APIDocumentParser
    from xextract.parsers.document.analyzer.doc_json import DocJsonAnalyzer

    logger.info(f"Parsing document: {input_file}")

    # 生成任务ID（如果未提供）
    if not task_id:
        task_id = str(uuid.uuid4())

    # 创建输出目录
    if output_dir:
        # 确保输出目录是绝对路径
        if not os.path.isabs(output_dir):
            output_dir = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), "..", output_dir
            )

        # 创建任务特定的子目录
        task_output_dir = os.path.join(output_dir, task_id)
        os.makedirs(task_output_dir, exist_ok=True)
        logger.info(f"Created output directory: {task_output_dir}")
    else:
        task_output_dir = None

    # 从配置文件中获取解析选项
    parser_api_url = config.document_parser.get(
        "api_base_url", "http://api.memect.cn:6111"
    )
    parser_api_key = config.document_parser.get("api_key", "")
    use_ocr = config.pdf2doc.get("ocr", False)
    table_option = config.pdf2doc.get("table", "all")
    layout_option = config.pdf2doc.get("layout", "auto")

    # 创建解析器
    parser = APIDocumentParser(
        api_base_url=parser_api_url,
        api_key=parser_api_key,
        extract_image=False,
        ocr=use_ocr,
        table=table_option,
        layout=layout_option,
    )

    logger.info(
        f"Document parser initialized with options - OCR: {use_ocr}, Tables: {table_option}, Layout: {layout_option}"
    )

    # 确定输出文件路径和格式
    output_format = format.lower()
    # 确定输出文件名
    base_filename = os.path.basename(input_file).split(".")[0]
    output_filename = f"{base_filename}.{output_format}"

    # 如果有任务输出目录，则使用它
    if task_output_dir:
        output_file = os.path.join(task_output_dir, output_filename)
    else:
        output_file = None

    # 解析文档
    if output_format == "json":
        # 直接解析到JSON
        result = asyncio.run(
            parser.parse_pdf(
                input_file,
                output_path=output_file,
            )
        )

        if output_file:
            logger.info(f"JSON result saved to {output_file}")
        else:
            # 打印结果到控制台
            print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        # 先解析到临时JSON文件
        temp_json = f"temp_doc_json_{int(time.time())}.json"
        asyncio.run(parser.parse_pdf(input_file, output_path=temp_json))
        # 使用DocJsonAnalyzer分析JSON文件
        doc_parser = DocJsonAnalyzer().analyze(temp_json)

        if output_format in ["md", "md_with_id"]:
            # 转换为Markdown，带有ID值。
            if output_format == "md_with_id":
                markdown_content = doc_parser.to_md_with_id()
            else:
                markdown_content = doc_parser.to_md()
            if output_file:
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(markdown_content)
                logger.info(f"Markdown result saved to {output_file}")
            else:
                print(markdown_content)
        elif output_format == "csv":
            # 转换为CSV
            if output_file:
                doc_parser.to_csv(output_file)
                logger.info(f"CSV result saved to {output_file}")
            else:
                # 打印CSV内容到控制台
                df = doc_parser.get_doc_table()
                out_df = doc_parser.ensure_dataframe(df)
                print(out_df.to_csv())

        # 清理临时文件
        if os.path.exists(temp_json):
            os.remove(temp_json)

    # 创建任务信息文件
    if task_output_dir:
        task_info_path = os.path.join(task_output_dir, "task_info.json")
        task_info = {
            "task_id": task_id,
            "timestamp": time.time(),
            "file": os.path.basename(input_file),
            "format": output_format,
            "status": "completed",
        }
        with open(task_info_path, "w", encoding="utf-8") as f:
            json.dump(task_info, f, indent=2, ensure_ascii=False)

    return 0


@cli.command(help="比对提取结果")
@click.option(
    "--baseline",
    "-b",
    required=True,
    help="Baseline result file or directory (old model)",
)
@click.option(
    "--current",
    "-c",
    required=True,
    help="Current result file or directory (new model)",
)
@click.option(
    "--output-dir",
    "-o",
    default="output",
    help="Output directory for comparison results",
)
@click.option(
    "--format",
    "-f",
    type=click.Choice(["json", "csv", "markdown"]),
    default="json",
    help="Output format",
)
@click.option(
    "--summary",
    "-s",
    help="Summary report file path (only valid when comparing directories)",
)
@click.option(
    "--pattern", "-p", default="*.json", help="File pattern for directory comparison"
)
@click.option("--task-id", help="Task ID for tracking (generated if not provided)")
@click.pass_context
def compare(ctx, baseline, current, output_dir, format, summary, pattern, task_id):
    """比对提取结果。"""
    debug = ctx.obj.get("DEBUG", False)

    # 导入比对模块
    from src.xextract.cli.compare import main as compare_main

    # 生成任务ID（如果未提供）
    if not task_id:
        task_id = f"compare_{int(time.time())}"

    # 创建输出目录
    if output_dir:
        # 确保输出目录是绝对路径
        if not os.path.isabs(output_dir):
            output_dir = os.path.join(
                os.path.dirname(os.path.abspath(__file__)), "..", output_dir
            )

        # 创建任务特定的子目录
        task_output_dir = os.path.join(output_dir, task_id)
        os.makedirs(task_output_dir, exist_ok=True)
        logger.info(f"Created output directory: {task_output_dir}")

        # 设置输出文件路径
        output_file = os.path.join(task_output_dir, f"comparison_result.{format}")

        # 如果未指定摘要文件，则在任务目录中创建
        if not summary and os.path.isdir(baseline):
            summary = os.path.join(task_output_dir, "comparison_summary.json")
    else:
        task_output_dir = None
        output_file = None

    # 构建参数列表
    compare_args = ["--baseline", baseline, "--current", current]

    if output_file:
        compare_args.extend(["--output", output_file])

    if format:
        compare_args.extend(["--format", format])

    if summary:
        compare_args.extend(["--summary", summary])

    if pattern:
        compare_args.extend(["--pattern", pattern])

    if debug:
        compare_args.append("--debug")

    # 运行比对命令
    result = compare_main(compare_args)

    # 创建任务信息文件
    if task_output_dir:
        task_info_path = os.path.join(task_output_dir, "task_info.json")
        task_info = {
            "task_id": task_id,
            "timestamp": time.time(),
            "baseline": os.path.basename(baseline),
            "current": os.path.basename(current),
            "format": format,
            "status": "completed" if result == 0 else "failed",
        }
        with open(task_info_path, "w", encoding="utf-8") as f:
            json.dump(task_info, f, indent=2, ensure_ascii=False)

    return result


def main():
    """Main entry point for XExtract."""
    return cli(obj={})


if __name__ == "__main__":
    sys.exit(main())
